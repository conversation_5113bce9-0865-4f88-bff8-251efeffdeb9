import React, { useState,useEffect } from "react";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../../services/API";
import { AUDIT_FINDINGS_ASSIGN_ACTION_URL, AUDIT_GMS3_URL, AUDIT_WITH_ID_URL, GET_USERS_BY_ROLE, INSPECTION_ACTION_PLAN_REVIEWER, STATIC_URL, AUDIT_GMS3_WITH_ID_URL, AUDIT_GMS2_WITH_ID_URL } from "../../constants";

const GoodPracticesForm = ({ selectedAuditGMSOne, gms, aformData, handleChange, handleFileChange, selectedAuditGmsThree }) => {
    // Form fields for Good Practices
    const [file, setFile] = useState([])
    const [gmsThreeData, setGmsThreeData] = useState(null)
    const [gmsTwoTitle, setGmsTwoTitle] = useState('')

    const fetchImageFile = async (url) => {
        const response = await fetch(url);
        const blob = await response.blob();
        const file = new File([blob], url.split('/').pop(), { type: blob.type });
        console.log(file)
        return file;
    };

    // Fetch GMS 3 data and then GMS 2 title
    useEffect(() => {
        const fetchGmsTwoTitle = async () => {
            if (selectedAuditGmsThree) {
                try {
                    // Get GMS 3 data
                    const gmsThreeResponse = await API.get(AUDIT_GMS3_WITH_ID_URL(selectedAuditGmsThree));
                    if (gmsThreeResponse.status === 200) {
                        const gmsThreeItem = gmsThreeResponse.data;
                        setGmsThreeData(gmsThreeItem);

                        // Get GMS 2 data using auditGmsTwoId
                        if (gmsThreeItem.auditGmsTwoId) {
                            const gmsTwoResponse = await API.get(AUDIT_GMS2_WITH_ID_URL(gmsThreeItem.auditGmsTwoId));
                            if (gmsTwoResponse.status === 200) {
                                setGmsTwoTitle(gmsTwoResponse.data.name);
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error fetching GMS data:', error);
                }
            }
        };

        fetchGmsTwoTitle();
    }, [selectedAuditGmsThree]);

    useEffect(() => {

        const addFiles = async () => {
            const urls = aformData.uploads ? aformData.uploads.map(i => {
                return `${STATIC_URL}/${i}`
            }) : []
            const fetchedFiles = await Promise.all(urls.map(url => fetchImageFile(url)));
            console.log(fetchedFiles)
            setFile(fetchedFiles)

        }
        addFiles();

    }, [aformData.uploads])


    return (
        <form>
            {/* Add your form fields here */}
            <h2>{gmsTwoTitle || 'Good Practices Form'}</h2>


            <div className="mb-3">
                <label htmlFor="evidencePhotos" className="form-label">
                    Upload Photos
                </label>
                {file.length !== 0 &&
                    <DropzoneArea
                        initialFiles={file}
                        acceptedFiles={[
                            'image/jpeg',
                            'image/png'
                        ]}
                        dropzoneText={"Drag and Drop Evidence Images"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={handleFileChange}
                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                    />
                }
                {file.length === 0 &&
                    <DropzoneArea
                        acceptedFiles={[
                            'image/jpeg',
                            'image/png'
                        ]}
                        dropzoneText={"Drag and Drop Evidence Images"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={handleFileChange}
                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                    />
                }
            </div>

            {/* Findings */}
            <div className="mb-3">
                <label htmlFor="findings" className="form-label">
                    Findings
                </label>
                <textarea
                    className="form-control"
                    id="findings"
                    name="findings"
                    onChange={handleChange}
                    value={aformData.findings}
                ></textarea>
            </div>

        </form>
    );
};

export default GoodPracticesForm;